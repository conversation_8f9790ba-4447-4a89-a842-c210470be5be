import { CallParams, Network } from "@/types/network.ts";
import { isAddress } from "viem";

export class EvmNetwork extends Network {
  constructor(config: Partial<Network>) {
    super(config);
  }

  async multicall(calls: CallParams[]): Promise<any[]> {
    if(!this.multiCall) return
    //@ts-ignore
    const res = await this.multiCall.all(calls.map((i) => this.readMultiContract(i)));
    res.forEach((v, i) => {
      const callback = calls[i].handler;
      if (typeof callback == 'function') {
        //@ts-ignore
        callback(v);
      } else {
        if (callback.setValue) {
          callback.setValue(new BigNumber(v.toString()));
        }
      }
    });
    return res;
  }

  isAddressValid(address: string): boolean {
    return isAddress(address);
  }
}
