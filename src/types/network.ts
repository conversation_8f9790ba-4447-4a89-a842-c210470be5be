import { makeObservable } from "mobx";

export abstract class Network {
  name: string = "";
  fullName: string = "";
  chainId: number = 0;
  logoUrl: string = "";
  rpcUrl: string = "";
  explorerName: string = "";
  explorerURL: string = "";
  destNetworks: Network[] = [];

  constructor(config: Partial<Network>) {
    Object.assign(this, config);
    makeObservable(this);
  }

  abstract multicall(calls: Partial<CallParams>[]): Promise<any[]>;

  abstract isAddressValid(address: string): boolean;
}

export interface CallParams<P = any[]> {
  address: string;
  abi: any;
  method: string;
  params?: P;
  options?: Partial<{
    value: string;
    gasLimit: string;
    gasPrice: string;
  }>;
  handler?: any;
  read?: boolean;
}